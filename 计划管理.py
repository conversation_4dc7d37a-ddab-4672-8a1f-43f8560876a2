#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QComboBox,
                            QPushButton, QFrame, QCheckBox, QLineEdit, QSpinBox,
                            QGroupBox, QFormLayout, QGridLayout, QSpacerItem, QSizePolicy,
                            QMessageBox, QScrollArea)
from PyQt5.QtGui import QIcon, QColor, QFont, QPalette, QPainter, QPainterPath, QPen
from PyQt5.QtCore import Qt, QSize, pyqtSignal

def get_config_path(relative_path):
    """
    获取配置文件的绝对路径，确保使用exe同目录的config

    参数:
        relative_path (str): 相对于config目录的文件路径

    返回:
        str: 配置文件的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        app_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        app_dir = os.path.dirname(os.path.abspath(__file__))

    config_path = os.path.join(app_dir, 'config', relative_path)
    return config_path

class PlanManager(QWidget):
    """计划管理界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedSize(1498, 708)  # 设置窗口大小
        self.initUI()
        
    def initUI(self):
        """初始化界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 顶部搜索区域
        self.create_top_search_area(main_layout)
        
        # 中间内容区域（表格 + 右侧功能区）
        content_layout = QHBoxLayout()
        
        # 左侧表格区域
        self.create_table_area(content_layout)
        
        # 右侧功能区域
        self.create_right_function_area(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # 底部按钮区域
        self.create_bottom_buttons(main_layout)
        
    def create_top_search_area(self, main_layout):
        """创建顶部搜索区域"""
        top_frame = QFrame()
        top_frame.setFixedHeight(50)
        top_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        top_layout = QHBoxLayout(top_frame)
        top_layout.setContentsMargins(15, 10, 15, 10)
        top_layout.setSpacing(15)
        
        # 商品ID搜索
        product_label = QLabel("商品ID")
        product_label.setStyleSheet("font-weight: bold; color: #333;")
        self.product_input = QLineEdit()
        self.product_input.setPlaceholderText("输入商品ID进行搜索")
        self.product_input.setFixedWidth(200)
        self.product_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 5px;
                font-size: 12px;
            }
        """)
        
        product_search_btn = QPushButton("商品搜索")
        product_search_btn.setFixedSize(80, 30)
        product_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a6be0;
            }
        """)
        
        # 店铺选择
        shop_label = QLabel("店铺")
        shop_label.setStyleSheet("font-weight: bold; color: #333;")
        self.shop_combo = QComboBox()
        self.shop_combo.setFixedWidth(200)
        self.shop_combo.setEditable(True)  # 设置为可编辑，支持搜索
        self.shop_combo.setInsertPolicy(QComboBox.NoInsert)  # 不允许插入新项
        self.shop_combo.completer().setCompletionMode(self.shop_combo.completer().PopupCompletion)  # 设置自动完成模式
        self.shop_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 5px;
                font-size: 12px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 8px solid #666;
                margin-right: 8px;
            }
            QComboBox::down-arrow:hover {
                border-top-color: #4e7ae7;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                background-color: white;
                selection-background-color: #4e7ae7;
                selection-color: white;
                outline: none;
                font-size: 12px;
            }
            QComboBox QAbstractItemView::item {
                height: 35px;
                min-height: 35px;
                padding: 8px 12px;
                border-bottom: 1px solid #f0f0f0;
                background-color: white;
                color: #333;
                font-size: 12px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f0f8ff;
                color: #333;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #4e7ae7;
                color: white;
                font-weight: bold;
            }
        """)

        # 加载店铺选项
        self.load_shop_options()

        # 设置自定义视图（必须在设置样式之前）
        from PyQt5.QtWidgets import QListView
        self.shop_combo.setView(QListView())

        # 设置搜索筛选功能
        self.setup_shop_search()
        
        # 添加到布局
        top_layout.addWidget(product_label)
        top_layout.addWidget(self.product_input)
        top_layout.addWidget(product_search_btn)
        top_layout.addSpacing(30)
        top_layout.addWidget(shop_label)
        top_layout.addWidget(self.shop_combo)
        top_layout.addStretch()
        
        main_layout.addWidget(top_frame)
        
    def create_table_area(self, content_layout):
        """创建表格区域"""
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(10, 10, 10, 10)
        
        # 表格
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(8)
        self.table_widget.setHorizontalHeaderLabels([
            "选择", "商品ID", "商品标题", "价格", "佣金", "推广状态", "操作状态", "店铺"
        ])
        
        # 设置表格样式
        self.table_widget.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                border: none;
            }
            QHeaderView::section {
                background-color: white;
                padding: 10px 8px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                border-right: 1px solid #e0e0e0;
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QTableWidget::item {
                border-bottom: 1px solid #f0f0f0;
                padding: 8px;
            }
        """)
        
        # 设置表格属性
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setSelectionBehavior(QTableWidget.SelectRows)
        self.table_widget.horizontalHeader().setStretchLastSection(True)

        # 添加状态栏（在表格顶部）
        self.create_status_bar(table_layout)

        table_layout.addWidget(self.table_widget)

        content_layout.addWidget(table_frame, 3)  # 占3/4宽度

    def create_status_bar(self, table_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFixedHeight(30)
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 1px solid #e0e0e0;
                border-radius: 0px;
            }
        """)

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)
        status_layout.setSpacing(10)

        # 左侧：商品加载进度（默认隐藏）
        self.progress_label = QLabel("商品加载进度：准备中...")
        self.progress_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
            }
        """)
        self.progress_label.hide()  # 默认隐藏

        # 右侧：商品统计（显示表格实际数据）
        self.stats_label = QLabel("总商品：0 | 已选择商品：0")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #333;
                font-size: 12px;
                font-weight: bold;
            }
        """)

        # 添加到布局
        status_layout.addWidget(self.progress_label)
        status_layout.addStretch()  # 弹性空间，将右侧内容推到右边
        status_layout.addWidget(self.stats_label)

        table_layout.addWidget(status_frame)

    def create_right_function_area(self, content_layout):
        """创建右侧功能区域"""
        right_frame = QFrame()
        right_frame.setFixedWidth(300)
        right_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(15, 15, 15, 15)
        right_layout.setSpacing(15)
        
        # 佣金筛选
        commission_group = QGroupBox()
        commission_layout = QVBoxLayout(commission_group)

        # 标题区域
        commission_title_layout = QHBoxLayout()
        commission_title = QLabel("佣金筛选")
        commission_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333;
                font-weight: bold;
                border: none;
                background: transparent;
            }
        """)
        commission_desc = QLabel("(勾选佣金等于输入值的商品)")
        commission_desc.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        commission_title_layout.addWidget(commission_title)
        commission_title_layout.addWidget(commission_desc)
        commission_title_layout.addStretch()
        commission_layout.addLayout(commission_title_layout)

        commission_input_layout = QHBoxLayout()
        self.commission_input = QLineEdit()
        self.commission_input.setPlaceholderText("输入佣金值...")
        self.commission_input.setFixedWidth(120)
        self.commission_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)
        commission_filter_btn = QPushButton("佣金筛选")
        commission_filter_btn.setFixedWidth(80)  # 设置固定宽度
        commission_filter_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
        """)

        commission_input_layout.addWidget(self.commission_input)
        commission_input_layout.addStretch()  # 添加弹性空间，将按钮推到右侧
        commission_input_layout.addWidget(commission_filter_btn)

        # 设置布局间距
        commission_input_layout.setSpacing(5)  # 设置统一间距

        commission_layout.addLayout(commission_input_layout)

        right_layout.addWidget(commission_group)

        # 价格筛选
        price_filter_group = QGroupBox()
        price_filter_layout = QVBoxLayout(price_filter_group)

        # 标题区域
        price_filter_title_layout = QHBoxLayout()
        price_filter_title = QLabel("价格筛选")
        price_filter_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333;
                font-weight: bold;
                border: none;
                background: transparent;
            }
        """)
        price_filter_desc = QLabel("(勾选价格在区间内的商品)")
        price_filter_desc.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        price_filter_title_layout.addWidget(price_filter_title)
        price_filter_title_layout.addWidget(price_filter_desc)
        price_filter_title_layout.addStretch()
        price_filter_layout.addLayout(price_filter_title_layout)

        price_filter_input_layout = QHBoxLayout()
        self.price_min_input = QLineEdit()
        self.price_min_input.setPlaceholderText("最小")
        self.price_min_input.setFixedWidth(50)
        self.price_min_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        price_to_label = QLabel("至")
        price_to_label.setFixedWidth(20)  # 设置固定宽度
        price_to_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        price_to_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)

        self.price_max_input = QLineEdit()
        self.price_max_input.setPlaceholderText("最大")
        self.price_max_input.setFixedWidth(50)
        self.price_max_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        price_filter_btn = QPushButton("价格筛选")
        price_filter_btn.setFixedWidth(80)  # 设置固定宽度
        price_filter_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
        """)

        price_filter_input_layout.addWidget(self.price_min_input)
        price_filter_input_layout.addWidget(price_to_label)
        price_filter_input_layout.addWidget(self.price_max_input)
        price_filter_input_layout.addStretch()  # 添加弹性空间，将按钮推到右侧
        price_filter_input_layout.addWidget(price_filter_btn)

        # 设置布局间距
        price_filter_input_layout.setSpacing(5)  # 设置统一间距

        price_filter_layout.addLayout(price_filter_input_layout)

        right_layout.addWidget(price_filter_group)

        # 批量改佣金
        batch_commission_group = QGroupBox()
        batch_commission_layout = QVBoxLayout(batch_commission_group)

        # 标题区域
        batch_commission_title_layout = QHBoxLayout()
        batch_commission_title = QLabel("批量改佣金")
        batch_commission_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333;
                font-weight: bold;
                border: none;
                background: transparent;
            }
        """)
        batch_commission_desc = QLabel("(为选中商品设置统一佣金)")
        batch_commission_desc.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        batch_commission_title_layout.addWidget(batch_commission_title)
        batch_commission_title_layout.addWidget(batch_commission_desc)
        batch_commission_title_layout.addStretch()
        batch_commission_layout.addLayout(batch_commission_title_layout)

        batch_commission_input_layout = QHBoxLayout()
        self.batch_commission_input = QLineEdit()
        self.batch_commission_input.setPlaceholderText("输入佣金值...")
        self.batch_commission_input.setFixedWidth(120)
        self.batch_commission_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)
        batch_commission_btn = QPushButton("批量改佣金")
        batch_commission_btn.setFixedWidth(80)  # 设置固定宽度
        batch_commission_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
        """)

        batch_commission_input_layout.addWidget(self.batch_commission_input)
        batch_commission_input_layout.addStretch()  # 添加弹性空间，将按钮推到右侧
        batch_commission_input_layout.addWidget(batch_commission_btn)

        # 设置布局间距
        batch_commission_input_layout.setSpacing(5)  # 设置统一间距

        batch_commission_layout.addLayout(batch_commission_input_layout)

        right_layout.addWidget(batch_commission_group)

        # 价格范围设置
        price_range_group = QGroupBox()
        price_range_layout = QVBoxLayout(price_range_group)

        # 标题区域（带复选框）
        title_layout = QHBoxLayout()
        self.price_range_checkbox = QCheckBox("价格范围")
        self.price_range_checkbox.setStyleSheet("font-size: 12px; color: #333; font-weight: bold;")
        self.price_range_checkbox.setChecked(True)  # 默认勾选
        title_layout.addWidget(self.price_range_checkbox)
        title_layout.addStretch()
        price_range_layout.addLayout(title_layout)

        # 输入区域
        price_range_input_layout = QHBoxLayout()

        min_label = QLabel("最小")
        min_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        self.price_range_min_input = QLineEdit()
        self.price_range_min_input.setPlaceholderText("最小")
        self.price_range_min_input.setFixedWidth(50)
        self.price_range_min_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        max_label = QLabel("最大")
        max_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        self.price_range_max_input = QLineEdit()
        self.price_range_max_input.setPlaceholderText("最大")
        self.price_range_max_input.setFixedWidth(50)
        self.price_range_max_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        commission_label = QLabel("佣金")
        commission_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        self.price_range_commission_input = QLineEdit()
        self.price_range_commission_input.setPlaceholderText("佣金")
        self.price_range_commission_input.setFixedWidth(50)
        self.price_range_commission_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        price_range_input_layout.addWidget(min_label)
        price_range_input_layout.addWidget(self.price_range_min_input)
        price_range_input_layout.addWidget(max_label)
        price_range_input_layout.addWidget(self.price_range_max_input)
        price_range_input_layout.addWidget(commission_label)
        price_range_input_layout.addWidget(self.price_range_commission_input)

        # 设置布局间距
        price_range_input_layout.setSpacing(5)  # 设置统一间距

        price_range_layout.addLayout(price_range_input_layout)

        # 价格范围列表显示区域
        self.price_range_list_layout = QVBoxLayout()
        price_range_layout.addLayout(self.price_range_list_layout)

        # 加载已保存的价格范围
        self.load_price_ranges()

        # 底部添加按钮区域
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()  # 左侧弹性空间，将按钮推到右下角

        add_range_btn = QPushButton("添加")
        add_range_btn.setFixedSize(50, 25)
        add_range_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 13px;
            }
        """)
        add_range_btn.clicked.connect(self.add_price_range)
        bottom_layout.addWidget(add_range_btn)

        price_range_layout.addLayout(bottom_layout)

        right_layout.addWidget(price_range_group)

        right_layout.addStretch()
        content_layout.addWidget(right_frame, 1)  # 占1/4宽度
        
    def create_bottom_buttons(self, main_layout):
        """创建底部按钮区域"""
        bottom_frame = QFrame()
        bottom_frame.setFixedHeight(50)
        bottom_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(15, 10, 15, 10)
        bottom_layout.setSpacing(10)
        
        # 按钮列表
        buttons = [
            "全部商品", "未设置商品", "全部未设置商品",
            "已下架推广", "添加推广", "重新上架"
        ]
        
        for btn_text in buttons:
            btn = QPushButton(btn_text)
            btn.setFixedHeight(30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f5f5f5;
                    color: #333;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    padding: 5px 15px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)
            bottom_layout.addWidget(btn)
        
        bottom_layout.addStretch()
        main_layout.addWidget(bottom_frame)

    def load_shop_options(self):
        """从配置文件加载店铺选项"""
        try:
            config_path = get_config_path('账号管理.json')

            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                self.shop_combo.addItem("无可用店铺")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 获取店铺数据
            shops_data = data.get('data', []) if isinstance(data, dict) else data

            # 清空现有选项
            self.shop_combo.clear()

            # 添加默认选项
            self.shop_combo.addItem("请选择店铺")

            # 添加店铺选项
            for shop in shops_data:
                if isinstance(shop, dict):
                    shop_name = shop.get('店铺名称', '').strip()
                    if shop_name:
                        self.shop_combo.addItem(shop_name)

            print(f"加载了 {self.shop_combo.count() - 1} 个店铺选项")

        except Exception as e:
            print(f"加载店铺选项失败: {str(e)}")
            self.shop_combo.clear()
            self.shop_combo.addItem("加载失败")

    def show_loading_progress(self, message="正在加载商品数据..."):
        """显示加载进度"""
        self.progress_label.setText(message)
        self.progress_label.show()

    def hide_loading_progress(self):
        """隐藏加载进度"""
        self.progress_label.hide()

    def update_stats(self, total_count=0, selected_count=0):
        """更新商品统计信息"""
        self.stats_label.setText(f"总商品：{total_count} | 已选择商品：{selected_count}")

    def get_table_stats(self):
        """获取表格统计信息"""
        total_count = self.table_widget.rowCount()
        selected_count = len(self.table_widget.selectionModel().selectedRows())
        return total_count, selected_count

    def refresh_stats(self):
        """刷新统计信息"""
        total_count, selected_count = self.get_table_stats()
        self.update_stats(total_count, selected_count)

    def setup_shop_search(self):
        """设置店铺搜索功能"""
        try:
            # 使用Qt内置的自动完成功能，更稳定
            from PyQt5.QtWidgets import QCompleter
            from PyQt5.QtCore import Qt

            # 获取所有店铺名称
            shop_names = []
            for i in range(self.shop_combo.count()):
                shop_names.append(self.shop_combo.itemText(i))

            # 创建自动完成器
            completer = QCompleter(shop_names)
            completer.setFilterMode(Qt.MatchContains)  # 包含匹配
            completer.setCaseSensitivity(Qt.CaseInsensitive)  # 大小写不敏感

            # 设置自动完成器
            self.shop_combo.setCompleter(completer)

            # 连接选择变化信号
            self.shop_combo.currentTextChanged.connect(self.on_shop_selected)

        except Exception as e:
            print(f"设置店铺搜索功能时出错: {str(e)}")

    def on_shop_selected(self, text):
        """店铺选择事件处理"""
        try:
            if text and text != "请选择店铺":
                print(f"选择了店铺: {text}")
                # 这里可以添加店铺选择后的逻辑
        except Exception as e:
            print(f"处理店铺选择时出错: {str(e)}")

    def get_config_path(self):
        """获取配置文件路径"""
        import os
        config_dir = os.path.join(os.path.dirname(__file__), "config")
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
        return os.path.join(config_dir, "cofig.json")

    def load_config(self):
        """加载配置文件"""
        try:
            import json
            config_path = self.get_config_path()
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            return {}

    def save_config(self, config):
        """保存配置文件（保留其他配置，只更新指定部分）"""
        try:
            import json
            config_path = self.get_config_path()

            # 先读取现有配置
            existing_config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)

            # 合并配置（保留现有配置，只更新传入的部分）
            existing_config.update(config)

            # 保存合并后的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
            return False

    def load_price_ranges(self):
        """加载价格范围列表"""
        try:
            config = self.load_config()
            price_ranges = config.get('price_ranges', [])

            # 清空现有列表
            self.clear_price_range_list()

            # 添加保存的价格范围
            for range_data in price_ranges:
                self.add_price_range_to_list(range_data)

        except Exception as e:
            print(f"加载价格范围失败: {str(e)}")

    def clear_price_range_list(self):
        """清空价格范围列表"""
        try:
            while self.price_range_list_layout.count():
                child = self.price_range_list_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        except Exception as e:
            print(f"清空价格范围列表失败: {str(e)}")

    def add_price_range_to_list(self, range_data):
        """添加价格范围到列表显示"""
        try:
            min_price = range_data.get('min_price', '')
            max_price = range_data.get('max_price', '')
            commission = range_data.get('commission', '')

            range_text = f"{min_price}-{max_price}元: {commission}%"
            range_label = QLabel(range_text)
            range_label.setStyleSheet("font-size: 13px; color: #666; padding: 2px;")
            self.price_range_list_layout.addWidget(range_label)

        except Exception as e:
            print(f"添加价格范围到列表失败: {str(e)}")

    def add_price_range(self):
        """添加价格范围"""
        try:
            # 获取输入值
            min_price = self.price_range_min_input.text().strip()
            max_price = self.price_range_max_input.text().strip()
            commission = self.price_range_commission_input.text().strip()

            # 验证输入
            if not min_price or not max_price or not commission:
                QMessageBox.warning(self, "提示", "请填写完整的价格范围信息")
                return

            try:
                float(min_price)
                float(max_price)
                float(commission)
            except ValueError:
                QMessageBox.warning(self, "提示", "请输入有效的数字")
                return

            if float(min_price) >= float(max_price):
                QMessageBox.warning(self, "提示", "最小价格必须小于最大价格")
                return

            # 创建价格范围数据
            range_data = {
                'min_price': min_price,
                'max_price': max_price,
                'commission': commission
            }

            # 加载现有配置
            config = self.load_config()
            if 'price_ranges' not in config:
                config['price_ranges'] = []

            # 添加新的价格范围
            config['price_ranges'].append(range_data)

            # 只保存price_ranges部分，不影响其他配置
            price_ranges_config = {'price_ranges': config['price_ranges']}

            # 保存配置
            if self.save_config(price_ranges_config):
                # 添加到列表显示
                self.add_price_range_to_list(range_data)

                # 清空输入框
                self.price_range_min_input.clear()
                self.price_range_max_input.clear()
                self.price_range_commission_input.clear()

                QMessageBox.information(self, "成功", "价格范围添加成功")
            else:
                QMessageBox.warning(self, "错误", "保存配置失败")

        except Exception as e:
            print(f"添加价格范围失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"添加价格范围失败: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = PlanManager()
    window.show()
    sys.exit(app.exec_())
