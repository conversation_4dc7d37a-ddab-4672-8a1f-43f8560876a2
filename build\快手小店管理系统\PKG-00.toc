('F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\快手小店管理系统.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('快手小店管理系统', 'F:\\desk\\WORK\\ksxiaodian\\快手小店管理系统.py', 'PYSOURCE'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\declarative_sensors.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\declarative_sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\dialogplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\dialogplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qtlabsplatformplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qtlabsplatformplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\sharedimageplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\sharedimageplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\declarative_bluetooth.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\declarative_bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qtqmlremoteobjects.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qtqmlremoteobjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qtquickcontrols2plugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qtquickcontrols2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qtquickextrasplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qtquickextrasplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\particlesplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\particlesplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qtquicktimelineplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qtquicktimelineplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qtquickcontrols2imaginestyleplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qtquickcontrols2imaginestyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\qtremoteobjects.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\qtremoteobjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qtquickcontrolsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qtquickcontrolsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmlshapesplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmlshapesplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qtgraphicaleffectsprivate.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qtgraphicaleffectsprivate.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qtquickextrasflatplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qtquickextrasflatplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qtquicktemplates2plugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qtquicktemplates2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qtgraphicaleffectsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qtgraphicaleffectsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmlwavefrontmeshplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmlwavefrontmeshplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\declarative_nfc.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\declarative_nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qquicklayoutsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qquicklayoutsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qtquick3dmaterialplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qtquick3dmaterialplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\declarative_positioning.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\declarative_positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\labsanimationplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\labsanimationplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\declarative_webchannel.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\declarative_webchannel.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qtquick3deffectplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qtquick3deffectplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\declarative_location.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\declarative_location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmllocalstorageplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmllocalstorageplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmlfolderlistmodelplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmlfolderlistmodelplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qtquick3dhelpersplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qtquick3dhelpersplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qtquickcontrols2universalstyleplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qtquickcontrols2universalstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\declarative_webview.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\declarative_webview.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qtquickcontrols2materialstyleplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qtquickcontrols2materialstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\dialogsprivateplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\dialogsprivateplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\locationlabsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\locationlabsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\declarative_qmlwebsockets.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\declarative_qmlwebsockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\labsmodelsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\labsmodelsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qtquickcontrols2fusionstyleplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qtquickcontrols2fusionstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmlsettingsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmlsettingsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\widgetsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\widgetsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\qmlplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\qmlplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\qquick3dplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\qquick3dplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\modelsplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\modelsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\qtquick2plugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\qtquick2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\qtwebengineplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\qtwebengineplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtTest\\qmltestplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\qmltestplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmlxmllistmodelplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmlxmllistmodelplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\workerscriptplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\workerscriptplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\windowplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\windowplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qtlabscalendarplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qtlabscalendarplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qtqmlstatemachine.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qtqmlstatemachine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\QtWebEngineProcess.exe',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\QtWebEngineProcess.exe',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom313.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\pywin32_system32\\pythoncom313.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_brotli.cp313-win_amd64.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\_brotli.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtNetwork.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQml.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtQml.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebChannel.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtWebChannel.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebEngineCore.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtWebEngineCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQuick.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtQuick.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPositioning.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtPositioning.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp313-win_amd64.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebEngineWidgets.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtWebEngineWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQuickWidgets.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtQuickWidgets.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebEngineCore.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebEngineCore.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebEngine.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebEngine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebEngineWidgets.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebEngineWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'BINARY'),
  ('config\\imges\\image.png',
   'F:\\desk\\WORK\\ksxiaodian\\config\\imges\\image.png',
   'DATA'),
  ('config\\imges\\ks.png',
   'F:\\desk\\WORK\\ksxiaodian\\config\\imges\\ks.png',
   'DATA'),
  ('config\\imges\\logo.ico',
   'F:\\desk\\WORK\\ksxiaodian\\config\\imges\\logo.ico',
   'DATA'),
  ('config\\imges\\minus_icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\config\\imges\\minus_icon.png',
   'DATA'),
  ('config\\imges\\plus_icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\config\\imges\\plus_icon.png',
   'DATA'),
  ('tool\\date.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\date.py', 'DATA'),
  ('tool\\内嵌浏览器.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\内嵌浏览器.py', 'DATA'),
  ('tool\\分类树.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\分类树.py', 'DATA'),
  ('tool\\店铺编辑器.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\店铺编辑器.py', 'DATA'),
  ('tool\\待下单底部.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\待下单底部.py', 'DATA'),
  ('tool\\状态管理器.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\状态管理器.py', 'DATA'),
  ('tool\\详情统计.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\详情统计.py', 'DATA'),
  ('tool\\阿里巴巴接口.py', 'F:\\desk\\WORK\\ksxiaodian\\tool\\阿里巴巴接口.py', 'DATA'),
  ('一键下单.py', 'F:\\desk\\WORK\\ksxiaodian\\一键下单.py', 'DATA'),
  ('商品复制.py', 'F:\\desk\\WORK\\ksxiaodian\\商品复制.py', 'DATA'),
  ('商品管理.py', 'F:\\desk\\WORK\\ksxiaodian\\商品管理.py', 'DATA'),
  ('店铺后台登录.py', 'F:\\desk\\WORK\\ksxiaodian\\店铺后台登录.py', 'DATA'),
  ('数据统计.py', 'F:\\desk\\WORK\\ksxiaodian\\数据统计.py', 'DATA'),
  ('登录窗口.py', 'F:\\desk\\WORK\\ksxiaodian\\登录窗口.py', 'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\py.typed',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('certifi\\py.typed',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DialogButtonBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageIndicatorSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedEmissiveMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Slider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive_mask.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive_mask.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperArtisticMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperArtisticMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchHandle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Desaturate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Desaturate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-vertical.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-vertical.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\materiallib.metainfo',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\materiallib.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_tr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ApplicationWindow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ItemDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolBarSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\check.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\check.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\editbox.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\editbox.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckDelegateSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Tumbler.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DelayButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\InsetSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\InsetSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\SignalSpy.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\SignalSpy.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-handle.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-handle.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\LabelSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\LabelSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Popup.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\crosshairs.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\crosshairs.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextArea.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Switch.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\FilePicker.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\FilePicker.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AuthenticationDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AuthenticationDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DelayButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\question.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\question.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\window_border.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\window_border.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SplitView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBarItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Frame.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwitchDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Page.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RoundButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextArea.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DelayButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Pane.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassRefractiveMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassRefractiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ProgressBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-right.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-right.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\information.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\information.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RangeSlider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\header.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\header.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ProgressBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\source\\custommaterial_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\source\\custommaterial_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Tumbler.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RoundButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaddingSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaddingSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ProgressBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\FastBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\FastBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\brushnoise.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\brushnoise.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Page.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolTip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextFieldSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextFieldSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ScrollViewSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ScrollViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\PageIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Page.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RoundButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RoundButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\GroupBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ThresholdMask.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ThresholdMask.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RecursiveBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RecursiveBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DelayButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Frame.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\WasdController.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\WasdController.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SpinBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AlertDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AlertDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\VerticalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Button.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RectangularGlow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RectangularGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_d.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_d.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-groove.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-groove.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\PromptDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\PromptDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SliderHandle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SliderHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_full_contrast.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_full_contrast.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SliderSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SliderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ItemDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Label.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\HDRBloomTonemap.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\HDRBloomTonemap.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\quick3d.metainfo',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\quick3d.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ja.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cone_model_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cone_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Pane.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RangeSlider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\HorizontalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ComboBoxSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ComboBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ColorOverlay.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ColorOverlay.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Switch.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SpinBoxSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SpinBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_diffuse.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_diffuse.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RoundButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionRipple.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionRipple.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_normal.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_normal.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\SteelMilledConcentricMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\SteelMilledConcentricMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RoundButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolSeparatorSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolSeparatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolTip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Label.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBarItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextField.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Switch.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DelayButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DelayButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchDelegateSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Blur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Blur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\groupbox.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\groupbox.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Container.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Container.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_es.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DialogButtonBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_fr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ProgressBarSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ProgressBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LevelAdjust.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LevelAdjust.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ActionGroup.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ActionGroup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ApplicationWindow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolTip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\StackView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSphere.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSphere.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Popup.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\meshes\\axisGrid.mesh',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\meshes\\axisGrid.mesh',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Pane.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextArea.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\HueSaturation.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\HueSaturation.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\GroupBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\StackView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Slider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\BusyIndicatorSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\BusyIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-down.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-down.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\question.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\question.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabBarSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\testlogger.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\testlogger.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Drawer.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\IdComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DialogButtonBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextField.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextField.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\AbstractButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\AbstractButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Label.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Tumbler.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\MotionBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\MotionBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ConfirmDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ConfirmDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient1D.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient1D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Colorize.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Colorize.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextArea.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_trans.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_trans.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\PageIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SplitView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Pane.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Pane.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchSelectionMenu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchSelectionMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RoundButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Flip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Flip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\Object3DSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\Object3DSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\StackView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_uk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ItemDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Glow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Glow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\warning.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\warning.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ItemDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\EdgeDetect.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\EdgeDetect.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Displace.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Displace.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Button.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\sunken_frame.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\sunken_frame.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Switch.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\VerticalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ButtonPanel.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ButtonPanel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\plane_model_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\plane_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\progress-indeterminate.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\progress-indeterminate.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\spherical_checker.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\spherical_checker.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_medium.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_medium.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderHandle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Label.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button_down.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button_down.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\GaussianBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\GaussianBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwipeDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Popup.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\IdComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Button.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\StackView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Label.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TumblerSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TumblerSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\GroupBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBarItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Page.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialGradient.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Drawer.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\copy.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\copy.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Frame.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ProgressBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\BusyIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GammaAdjust.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GammaAdjust.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_pl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Switch.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-horizontal.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-horizontal.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\MaskedBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\MaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\qtquickcontrols2.metainfo',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\qtquickcontrols2.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Emboss.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Emboss.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\VerticalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ColorDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\focusframe.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\focusframe.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DepthOfFieldHQBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DepthOfFieldHQBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\source\\effect_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\source\\effect_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DialogButtonBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolTip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\sphere_model_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\sphere_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\MaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\MaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DelayButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Frame.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Slider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\PromptDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\PromptDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\InnerShadow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\InnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwipeDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolTip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.jsc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\PageIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RangeSlider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Popup.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient4D.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient4D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_hu.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_bg.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\BusyIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ChromaticAberration.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ChromaticAberration.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextArea.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ElevationEffect.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ElevationEffect.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ContainerSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ContainerSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SpinBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Tumbler.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_fi.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ConfirmDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ConfirmDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab_selected.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab_selected.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\IdComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\slider_handle.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\slider_handle.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ko.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\DebugView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\DebugView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Scatter.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Scatter.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\rightanglearrow.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\rightanglearrow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\CopperMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\CopperMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RectangularGlow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RectangularGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Desaturate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Desaturate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\icons.ttf',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\icons.ttf',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\information.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\information.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AuthenticationDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AuthenticationDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient3D.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient3D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ApplicationWindow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SplitView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumEmissiveMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cube_model_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cube_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Vignette.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Vignette.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\SCurveTonemap.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\SCurveTonemap.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\critical.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\critical.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\VerticalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\HorizontalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ConicalGradient.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ConicalGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Action.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Action.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\StackViewSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\StackViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AlertDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AlertDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_trans.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_trans.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextField.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassSinglePassMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassSinglePassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\shadow.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\shadow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_sk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RangeSlider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_a.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_a.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\PageIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\effectlib.metainfo',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\effectlib.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\BusyIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Button.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Drawer.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\GroupBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Fxaa.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Fxaa.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkers.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkers.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\HorizontalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ColorMaster.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ColorMaster.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_b.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_b.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SplitView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Drawer.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\AbstractButtonSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\AbstractButtonSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Slider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SpinBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SpinBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.jsc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ru.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Control.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Control.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.js',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperOfficeMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperOfficeMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\GroupBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\white.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\white.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextAreaSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextAreaSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DirectionalBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DirectionalBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\AxisHelper.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\AxisHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\FrameSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\FrameSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Page.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SpinBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\GroupBoxSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\GroupBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RangeSlider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BoxShadow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BoxShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ApplicationWindow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GaussianBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GaussianBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Slider.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_large.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_large.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderGroove.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderGroove.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\TiltShift.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\TiltShift.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\knob.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\knob.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\HorizontalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_de.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dialog.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Blend.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Blend.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\OpacityMask.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\OpacityMask.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\BusyIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\Menu.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ApplicationWindow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\VerticalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-up.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-up.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\model16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\model16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ZoomBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ZoomBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CursorDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CursorDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_da.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumBrushedMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumBrushedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkmark.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkmark.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient2D.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient2D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Button.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\AdditiveColorGradient.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\AdditiveColorGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\BrushStrokes.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\BrushStrokes.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SplitView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ProgressBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LinearGradient.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LinearGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\PageIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedEmissiveMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\needle.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\needle.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeViewSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassMaterial.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel_aniso.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel_aniso.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\BrightnessContrast.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\BrightnessContrast.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BusyIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_lv.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeDelegateSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-left.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-left.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\WebSockets\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\WebSockets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-transient.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-transient.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\TestCase.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\TestCase.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\leftanglearrow.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\leftanglearrow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckBoxSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ComboBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBarItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioDelegateSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\plugins.qmltypes',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\view3D_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\view3D_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cylinder_model_template.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cylinder_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_en.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\HorizontalHeaderView.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolButton.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\qtquickextras.metainfo',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\qtquickextras.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShadowSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShadowSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DropShadow.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DropShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSpiral.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSpiral.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture16.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_small.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_small.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ItemDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Drawer.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ToolTip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\qmldir',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Tumbler.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextField.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DialogButtonBox.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuItem.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ButtonGroup.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ButtonGroup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.jsc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Frame.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RangeSliderSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RangeSliderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ToolTip.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSection.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qmlc',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwitchDelegate.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Popup.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuSeparator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioIndicator.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon.png',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabBar.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSpecifics.qml',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-US.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-US.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ru.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ru.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fr.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-TW.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-TW.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\el.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\el.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fil.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fil.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\bn.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\bn.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\cs.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\cs.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\nl.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\nl.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\et.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\et.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ro.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ro.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\pl.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\pl.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_de.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_pl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\uk.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\uk.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\de.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\de.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\hr.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\hr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\qt.conf',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\qt.conf',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-CN.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-CN.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_ca.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\id.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\id.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\hi.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\hi.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\te.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\te.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\lv.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\lv.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sv.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sv.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\vi.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\vi.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_resources.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_resources.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\es-419.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\es-419.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fa.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fa.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_devtools_resources.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_devtools_resources.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\th.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\th.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-GB.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-GB.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\lt.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\lt.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\da.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\da.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\am.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\am.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_resources_200p.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_resources_200p.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\it.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\it.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\hu.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\hu.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sw.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sw.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\he.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\he.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\icudtl.dat',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\resources\\icudtl.dat',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_en.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_ru.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_resources_100p.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_resources_100p.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_es.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\es.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\es.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-BR.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-BR.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\gu.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\gu.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_ko.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ar.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ar.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ja.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ja.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sk.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sk.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fi.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fi.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ms.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ms.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_uk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-PT.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-PT.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ko.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ko.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\nb.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\nb.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sr.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ta.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ta.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ca.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ca.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sl.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sl.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\kn.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\kn.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\tr.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\tr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\mr.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\mr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\bg.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\bg.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ml.pak',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ml.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_bg.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ru.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_fi.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_zh_TW.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_da.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ca.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ko.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_tr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ja.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_en.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_fr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_uk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_de.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ko.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_uk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_tr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_hu.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_de.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_es.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_bg.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_fr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_pl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ca.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_da.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_fi.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ru.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_en.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'F:\\desk\\WORK\\ksxiaodian\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('base_library.zip',
   'F:\\desk\\WORK\\ksxiaodian\\build\\快手小店管理系统\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
